# 学术写作模板系统 (Academic Writing Template System)

## 📁 模板结构概览

### **核心写作模板** (6个)
```
01-abstract-template.md           # 摘要模板 (支持医学/技术/微创医疗三导向)
02-introduction-template.md       # 引言模板 (新增技术演进逻辑链和地域差异分析，强化统计数据支撑)
03-materials-methods-template.md  # 材料方法模板 (支持制造/功能双导向)
04-results-template.md           # 结果模板总指导 (含子模板选择指南，新增统计分析模块)
05-discussion-template.md        # 讨论模板 (新增技术成熟度评估模块，强化临床转化分析)
06-conclusion-template.md        # 结论模板 (支持独立/合并两种方式)
```

### **材料方法部分子模板** (6个)
```
03.1-system-design-experimental-protocol.md      # 系统设计与实验方案 (新增)
03.2-materials-fabrication-processes.md          # 材料制备与制造工艺 (新增)
03.3-clinical-research-ethics-protocol.md        # 临床研究与伦理协议 (新增)
03.4-characterization-testing-analysis.md        # 表征测试与分析方法 (新增)
03.5-statistical-analysis-data-processing.md     # 统计分析与数据处理 (新增)
03.6-algorithm-implementation-signal-processing.md # 算法实现与信号处理 (新增)
```

### **结果部分子模板** (8个)
```
04.1-algorithm-signal-processing-results.md      # 算法与信号处理结果
04.2-multi-scenario-clinical-applications-results.md  # 多场景临床应用结果 (新增日常使用场景验证)
04.3-extended-data-supplementary-results.md      # 扩展数据与补充结果
04.4-mechanical-characterization-results.md      # 机械特性测试结果
04.5-results-discussion-combined-results.md      # 结果讨论合并展示
04.6-technology-comparison-results.md            # 技术对比分析结果
04.7-multi-technology-integration-results.md     # 多技术集成结果 (新增)
04.8-minimally-invasive-medical-results.md       # 微创医疗技术结果 (新增)
```

### **专门应用模板** (1个)
```
review-response-letter-template.md  # 审稿回复信模板
```

## 🎯 快速选择指南

### **按研究类型选择**

#### **临床医学研究** → Nature Medicine, NEJM
```
主模板: 01 + 02 + 03 + 04 + 05 + 06
子模板: 04.2 + 04.3
重点: 临床验证 + 详细数据
```

#### **工程技术创新** → Nature, Science
```
主模板: 01 + 02 + 03 + 04 + 05 + 06
子模板: 04.1 + 04.4 + 04.6
重点: 算法创新 + 性能测试 + 技术对比
```

#### **产品开发研究** → Applied Sciences
```
主模板: 01 + 02 + 03 + 04 + 05 + 06
子模板: 04.5 + 04.6
重点: 综合展示 + 市场分析
```

#### **基础技术研究** → IEEE Transactions
```
主模板: 01 + 02 + 03 + 04 + 05 + 06
子模板: 04.1 + 04.3
重点: 算法创新 + 技术细节
```

#### **微创医疗技术** → Nature Medicine, Science Translational Medicine
```
主模板: 01 + 02 + 03 + 04 + 05 + 06
子模板: 04.8 + 04.2 + 04.3
重点: 临床安全性 + 统计验证 + 监管合规
```

#### **多技术集成研究** → Science Advances, Nature
```
主模板: 01 + 02 + 03 + 04 + 05 + 06
子模板: 04.7 + 04.1 + 04.6
重点: 系统协同 + 集成创新 + 性能提升
```

## 📋 使用流程

### **步骤1: 确定研究导向**
- **医学导向**: 选择医学导向选项 (临床价值、患者获益)
- **技术导向**: 选择技术导向选项 (技术创新、商业价值)

### **步骤2: 选择结构方式**
- **传统分离式**: 结果→讨论→结论 (适合传统期刊)
- **现代合并式**: 结果讨论合并 (适合现代期刊)

### **步骤3: 配置子模板**
- 根据研究特点选择相应的04.X子模板
- 可单独使用或组合使用多个子模板

### **步骤4: 质量检查**
- 使用各模板的质量检查清单
- 确保内容完整性和逻辑连贯性

## 🔗 模板特色功能

### **双导向支持**
- **医学导向**: 死亡率统计、临床需求、患者获益
- **技术导向**: 技术分类、市场需求、商业价值

### **灵活结构选择**
- **分离式结构**: 传统的章节分离组织
- **合并式结构**: 现代的结果讨论合并

### **专业化子模板**
- **算法处理**: 详细的算法开发和验证指导
- **临床应用**: 多场景临床验证展示框架
- **机械测试**: 全面的机械性能验证
- **技术对比**: 系统性竞争分析框架

## 📊 模板适用性矩阵

| 研究类型 | 主要期刊 | 推荐模板组合 | 写作重点 |
|---------|---------|-------------|---------|
| 临床医学 | Nature Medicine | 04.2 + 04.3 | 临床验证 + 详细数据 |
| 工程技术 | Nature/Science | 04.1 + 04.4 + 04.6 | 算法 + 性能 + 对比 |
| 产品开发 | Applied Sciences | 04.5 + 04.6 | 应用 + 市场 |
| 基础研究 | IEEE Trans | 04.1 + 04.3 | 算法 + 技术细节 |
| 微创医疗 | Nature Medicine | 04.8 + 04.2 + 04.3 | 安全性 + 统计验证 |
| 多技术集成 | Science Advances | 04.7 + 04.1 + 04.6 | 系统协同 + 性能提升 |
| 多学科交叉 | Science Advances | 04.1 + 04.2 + 04.4 | 综合验证 |

## 💡 使用建议

### **新手建议**
1. 从单一子模板开始熟悉结构
2. 参考模板中的具体示例
3. 使用质量检查清单确保完整性

### **进阶使用**
1. 根据期刊要求灵活组合模板
2. 根据审稿意见调整模板选择
3. 自定义模板内容以适应特定需求

### **质量保证**
1. 每个模板都包含详细的使用说明
2. 提供基于真实论文的示例
3. 包含全面的质量检查清单

---

## 📝 更新日志

**v2.3** (当前版本)
- 全面重构材料方法模板系统：创建6个专门子模板覆盖不同研究需求
- 新增材料方法子模板：系统设计、材料制备、临床协议、表征测试、统计分析、算法实现
- 基于《用于长期颅内压监测的非手术式支架内膜生物电子系统》论文优化微创医疗技术模板
- 强化统计分析模块：详细的Spearman相关分析、Bland-Altman分析、ICC评估方法
- 完善临床研究协议：伦理审批、知情同意、安全监测、数据管理全流程
- 增强算法实现模板：信号处理、机器学习、实时处理、算法验证完整框架
- 建立模块化模板体系：主模板+子模板结构，支持灵活组合和定制化使用
- 提供详细使用指南：研究类型匹配、模板选择建议、写作流程指导

**v2.2** (前一版本)
- 基于《用于长期颅内压监测的非手术式支架内膜生物电子系统》论文深度优化模板
- 新增结果子模板：04.7多技术集成结果、04.8微创医疗技术结果
- 强化统计分析模块：新增Spearman相关分析、Bland-Altman分析、ICC评估
- 增强摘要模板：突出微创性特征和金标准对比验证，新增微创医疗导向
- 优化引言模板：强化数据支撑的局限性分析(感染率、并发症风险等)
- 完善结果模板：增加严谨的统计验证和可靠性评估框架
- 提升讨论模板：强化临床转化价值和监管审批路径分析
- 整合模板结构：将独立模板整合为结果子模板，提高系统一致性

**v2.1** (前一版本)
- 基于柔性可穿戴听诊器论文进一步完善模板系统
- 新增技术演进逻辑链和地域差异化需求分析模块
- 增加多技术集成展示专门模板
- 强化日常使用场景验证和技术成熟度评估
- 提升模板对复杂系统和跨学科研究的支持能力

**v2.0** (前一版本)
- 基于两篇高质量样本论文完善模板系统
- 重组结果模板为主模板+6个子模板结构
- 增加医学/技术双导向支持
- 新增机械测试、技术对比等专门模板

**v1.0** (初始版本)
- 基于Nature Medicine论文创建基础模板系统
- 包含6个核心写作模板和3个专门模板

---

通过这套完整的模板系统，研究者可以高效地撰写出符合不同期刊要求的高质量学术论文。

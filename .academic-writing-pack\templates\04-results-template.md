# 结果模板总指导 (Results Template Master Guide)

[[LLM: This master template provides comprehensive guidance for organizing results sections in academic papers. Based on analysis of high-impact publications, it offers multiple organizational approaches and specialized sub-templates for different research types.]]

## 模板说明
- **用途**: 学术论文结果部分的总体指导和子模板选择指南
- **适用范围**: 生物医学工程、技术创新、临床研究、产品开发等多领域
- **核心原则**: 灵活组织、专业针对、数据驱动、应用导向
- **组织方式**: 根据研究类型选择相应的子模板组合
- **写作风格**: 系统展示、逻辑清晰、验证充分、价值明确

## 📋 子模板选择指南

### **研究类型与推荐模板组合**

| 研究类型 | 主要内容 | 推荐子模板组合 | 适用期刊类型 |
|---------|---------|---------------|-------------|
| **临床医学研究** | 临床验证、患者数据、医学应用 | 4.2 + 4.3 | Nature Medicine, NEJM |
| **工程技术创新** | 算法开发、性能测试、技术对比 | 4.1 + 4.4 + 4.6 | Nature, Science |
| **产品开发研究** | 系统集成、商业应用、用户体验 | 4.5 + 4.6 | Applied Sciences |
| **基础技术研究** | 算法原理、信号处理、性能分析 | 4.1 + 4.3 | IEEE Transactions |
| **多技术集成研究** | 系统协同、集成创新、性能提升 | 4.7 + 4.1 + 4.6 | Science Advances |
| **微创医疗技术** | 安全性验证、统计分析、临床转化 | 4.8 + 4.2 + 4.3 | Nature Medicine |
| **多学科交叉** | 综合验证、多场景应用 | 4.1 + 4.2 + 4.4 | Science Advances |

### **子模板详细说明**

#### **4.1 算法与信号处理结果** (`04.1-algorithm-signal-processing-results.md`)
- **适用**: 算法开发、信号处理、机器学习验证
- **核心内容**: 算法性能、信号处理流程、分类准确性
- **关键指标**: 准确率、敏感性、特异性、计算效率

#### **4.2 多场景临床应用结果** (`04.2-multi-scenario-clinical-applications-results.md`)
- **适用**: 临床验证、多中心研究、患者群体研究
- **核心内容**: 临床试验结果、患者数据、多场景验证
- **关键指标**: 临床有效性、安全性、患者获益

#### **4.3 扩展数据与补充结果** (`04.3-extended-data-supplementary-results.md`)
- **适用**: 详细技术数据、补充验证、方法学验证
- **核心内容**: 详细实验数据、技术参数、统计分析
- **关键指标**: 重现性、可靠性、统计显著性

#### **4.4 机械特性测试结果** (`04.4-mechanical-characterization-results.md`)
- **适用**: 柔性设备、可穿戴技术、材料工程
- **核心内容**: 机械性能、耐久性测试、生物相容性
- **关键指标**: 弯曲性能、拉伸强度、长期稳定性

#### **4.5 结果讨论合并展示** (`04.5-results-discussion-combined-results.md`)
- **适用**: 现代期刊格式、技术创新研究、产品开发
- **核心内容**: 结果与分析并行、实时讨论、渐进论证
- **关键指标**: 技术优势、应用价值、商业潜力

#### **4.6 技术对比分析结果** (`04.6-technology-comparison-results.md`)
- **适用**: 竞争分析、市场定位、技术评估
- **核心内容**: 性能对比、优势分析、成本效益
- **关键指标**: 竞争优势、市场定位、商业可行性

#### **4.7 多技术集成结果** (`04.7-multi-technology-integration-results.md`)
- **适用**: 复杂系统研究、跨学科技术创新、集成化产品开发
- **核心内容**: 技术协同验证、系统性能提升、集成优势展示
- **关键指标**: 协同效应、系统级性能、应用拓展价值

#### **4.8 微创医疗技术结果** (`04.8-minimally-invasive-medical-results.md`)
- **适用**: 血管介入系统、植入式生物电子设备、微创监测技术
- **核心内容**: 安全性验证、统计分析、临床对比、监管合规
- **关键指标**: 生物相容性、统计一致性、临床优势、成本效益

## 🎯 使用指南

### **如何选择合适的子模板**

#### **步骤1: 确定研究类型**
- **临床导向**: 重点在医学应用和患者获益 → 选择4.2, 4.3
- **技术导向**: 重点在技术创新和性能优势 → 选择4.1, 4.4, 4.6
- **产品导向**: 重点在商业应用和市场价值 → 选择4.5, 4.6

#### **步骤2: 确定内容重点**
- **算法创新**: 4.1 算法与信号处理结果
- **临床验证**: 4.2 多场景临床应用结果
- **技术细节**: 4.3 扩展数据与补充结果
- **机械性能**: 4.4 机械特性测试结果
- **综合展示**: 4.5 结果讨论合并展示
- **竞争分析**: 4.6 技术对比分析结果

#### **步骤3: 组合使用建议**
- **单一模板**: 专注特定方面的研究
- **双模板组合**: 平衡技术和应用两个维度
- **多模板组合**: 全面展示复杂系统的多重价值

### **常见组合方案**

#### **方案A: 医学期刊组合** (Nature Medicine, NEJM)
```
主体结构: 传统分离式
推荐组合: 4.2 + 4.3
重点内容: 临床验证 + 详细数据
写作风格: 医学导向、患者获益
```

#### **方案B: 技术期刊组合** (Nature, Science)
```
主体结构: 传统分离式或合并式
推荐组合: 4.1 + 4.4 + 4.6
重点内容: 算法创新 + 性能测试 + 技术对比
写作风格: 技术导向、创新突出
```

#### **方案C: 应用期刊组合** (Applied Sciences, Engineering)
```
主体结构: 合并式
推荐组合: 4.5 + 4.6
重点内容: 综合展示 + 市场分析
写作风格: 应用导向、商业价值
```

## 📝 基础结构模板

### **通用开篇结构**
[[LLM: Provide flexible opening structure that can be adapted for different research types.]]

**模板结构**:
```
**图{{figure_number}}**展示了{{system_description}}的{{key_aspects}}。{{system_characteristics}}包括{{component_list}}，这些{{capabilities}}实现了{{primary_functions}}。{{performance_summary}}表明该系统在{{application_areas}}方面具有{{performance_characteristics}}。
```

**写作要点**:
- 提供系统的整体概览
- 突出关键技术特征
- 强调主要功能和性能
- 建立后续详细展示的基础

**示例**:
```
**图1**展示了BAMS网络系统的三个临床相关应用。系统特征包括宽带声学传感、无线时间同步和多点监测，这些能力实现了连续生理信号监测。初步验证表明该系统在临床准确性和用户接受度方面具有优异性能。
```

**新增样本论文风格示例**:
```
**图1**展示了改善新生儿院间监护的智能可穿戴生物电子系统概览。系统特征包括胸部生物贴片、额头脉搏血氧仪和云端集成平台，这些组件实现了心率、呼吸频率、皮肤温度、体位及血氧饱和度的精准监测。该系统采用${2.45}\mathrm{{GHz}}$蓝牙传输，平均功耗约5$\mathrm{{mW}}$，单次充电可实现超过${22}\mathrm{\;h}$的持续运行。
```

### 1.2 设备形态与用户体验展示
[[LLM: Show device form factor, user experience, and comparison with traditional methods through visual evidence.]]

**模板结构**:
```
图{{figure_number}}中的照片突出显示{{device_characteristics}}与{{application_area}}的密切接触。图{{figure_number}}显示了{{substrate_material}}基底上{{device_description}}的正面，易于操作。图{{figure_number}}中的照片显示了设备背面集成的{{contact_components}}，具有极高的{{mechanical_properties}}。总的来说，{{system_characteristics}}提供用户{{user_benefits}}，使用户能够轻松依照指示在{{usage_location}}测量{{target_parameters}}，无需{{eliminated_requirements}}。{{demonstration_material}}中的示例展示了用户可轻松操作的{{operation_characteristics}}，相比复杂且耗时的{{traditional_method}}设置。
```

**写作要点**:
- 用多角度照片展示设备特征
- 强调与应用部位的贴合性
- 突出机械性能和操作便利性
- 对比传统方法的复杂性

**示例**:
```
图1（B和C）中的照片突出显示可穿戴贴片与面部（尤其是额头和下巴）的密切接触。图1D显示了聚四氟乙烯（PTFE）基底上高度软性膜贴片的正面，易于操作。图1（E和F）中的照片显示了设备背面集成的与皮肤接触的纳米膜电极，具有极高的拉伸性和柔韧性。总的来说，软可穿戴平台提供用户舒适、易用和便携性，使用户能够轻松依照指示在家中测量睡眠，无需技术人员。影片S1中的示例展示了用户可轻松操作的设备手册，相比复杂且耗时的多导睡眠图设置。
```

## 2. 制造工艺与技术创新

### 2.1 可扩展制造工艺展示
[[LLM: Present manufacturing processes with visual evidence of scalability and precision.]]

**模板结构**:
```
图{{figure_number}}中的照片展示了一个{{component_array}}，位于一个{{substrate_size}}上，以及{{component_description}}的对应显微图像，具有{{pattern_characteristics}}，提供增强的{{performance_benefits}}。用于制造的{{fabrication_parameters}}，每个{{component_name}}的图案宽度为{{pattern_width}}，提供足够的{{functional_requirements}}。图{{figure_number}}中的照片展示了一个在{{large_substrate}}上制造的{{component_system}}，可在多个{{connection_targets}}之间提供{{connection_function}}。
```

**写作要点**:
- 展示大规模制造的能力
- 提供精确的制造参数
- 突出图案化的精度和功能
- 体现制造工艺的可扩展性

**示例**:
```
图2（C和D）中的照片展示了一个金电极阵列，位于一个5英寸正方形板上，以及电极的对应显微图像，具有曲线图案，提供增强的可拉伸性和机械可靠性。用于制造的激光点尺寸为13微米，每个电极的图案宽度为124微米，提供足够的皮肤接触。图2（E和F）中的照片展示了一个在大玻璃（8英寸乘10英寸）上制造的铜互连器组，可在多个电极和集成电路之间提供电气连接。
```

### 2.2 材料集成与封装技术
[[LLM: Show material integration and packaging innovations with mechanical performance data.]]

**模板结构**:
```
{{demonstration_material}}中的示例展示了我们如何使用{{fabrication_method}}制造{{system_components}}的关键部件。{{component_connection}}通过{{bonding_method}}与{{connection_elements}}连接，当安装在{{substrate_material}}上时保持{{mechanical_stability}}。与传统的{{traditional_material}}相比，{{composite_material}}的优势在于其{{mechanical_advantages}}，由于其由{{structural_characteristics}}组成的{{structural_type}}，不易{{structural_problems}}。另一个优势是{{bonding_material}}穿透{{substrate_structure}}，实现{{integration_benefits}}，同时使{{surface_characteristics}}，便于处理。
```

**写作要点**:
- 展示关键制造步骤的视频证据
- 强调材料集成的技术优势
- 对比传统材料的局限性
- 突出结构设计的创新性

**示例**:
```
影片S3中的示例展示了我们如何使用激光制造可穿戴系统的关键部件。电极通过焊接与可拉伸导线连接，当安装在软织物上时保持机械稳定性。与传统的编织织物相比，织物复合材料的优势在于其全向弹性和便于加工性，由于其由随机网络纤维组成的非编织结构，不易缠结。另一个优势是软硅胶粘合剂（Silbione）穿透织物纤维网络，实现双层结构的机械锁定，同时使织物顶部干燥且不粘，便于处理。
```

## 3. 机械性能与材料表征

### 3.1 计算建模验证
[[LLM: Present computational modeling results with experimental validation of mechanical performance.]]

**模板结构**:
```
本研究通过{{modeling_approach}}和实验验证捕捉了开发的{{component_systems}}的机械特性。图{{figure_number}}中的{{analysis_type}}结果展示了设计结构的{{mechanical_capability}}，可达{{performance_limit}}，显示{{material_1}}和{{material_2}}的最大应变远低于{{failure_criteria}}。额外的{{analysis_type}}表明，{{component_1}}和{{component_2}}在破裂和屈服之前可以分别伸展至{{limit_1}}和{{limit_2}}。结构在破裂前能够承受的最大拉伸应变超过{{ultimate_limit}}。
```

**写作要点**:
- 结合计算建模和实验验证
- 提供具体的性能极限数据
- 对比不同材料的失效标准
- 强调结构的极限承载能力

**示例**:
```
本研究通过计算建模和实验验证捕捉了开发的软电极和互连器的机械特性。图3（A和B）中的有限元分析结果展示了设计结构的机械可伸展性，可达30%，显示金膜和铜膜的最大应变远低于破裂应变和屈服应变（分别为1%和0.3%）。额外的有限元分析表明，电极和连接器在破裂和屈服之前可以分别伸展至108%和110%。结构在破裂前能够承受的最大拉伸应变超过200%。
```

### 3.2 实验机械性能验证
[[LLM: Present experimental mechanical testing results with reliability and durability data.]]

**模板结构**:
```
图{{figure_number}}中的实验研究验证了拉伸测试期间制作组件的机械可靠性。测试券包括{{test_components}}，以便进行更准确的拉伸测量。没有观察到任何{{failure_modes}}。图{{figure_number}}中的{{component_systems}}的循环拉伸和电性能测量证明了其可以多次使用的安全性。通过{{cycle_count}}次{{strain_level}}的拉伸循环，{{performance_metric}}几乎没有变化，并间歇性出现的噪点。实验证实了{{material_system}}的{{mechanical_characteristics}}，显示{{material_system}}的可拉伸性高达{{stretchability_limit}}，杨氏模量为{{elastic_modulus}}，泊松比为{{poisson_ratio}}。
```

**写作要点**:
- 展示实验验证的可靠性
- 提供循环测试的耐久性数据
- 报告材料的基本力学性能
- 强调多次使用的安全性

**示例**:
```
图3C中的实验研究验证了拉伸测试期间制作组件的机械可靠性。测试券包括金电极和铜连接器，以便进行更准确的拉伸测量（图3C的插图）。没有观察到任何破裂或屈服特征。图3D中的电极和连接器的循环拉伸和电性能测量证明了其可以多次使用的安全性。通过1000次30%的拉伸循环，电阻几乎没有变化，并间歇性出现的噪点。实验证实了织物的全方向可伸缩性和弹性，显示织物的可拉伸性高达300%，杨氏模量为1.29 MPa，泊松比为0.184。
```

## 4. 临床验证与金标准对比

### 4.1 临床研究设置与信号质量对比
[[LLM: Present clinical validation setup and signal quality comparison with gold standard methods.]]

**模板结构**:
```
本研究通过将制作的{{device_description}}与黄金标准{{gold_standard_system}}设置进行横向比较，展示了其性能。图{{figure_number}}中的照片展示了{{clinical_setting}}中的一位参与者戴着两种不同系统；{{device_placement_1}}和{{device_placement_2}}上有{{device_count}}个不显眼的{{device_type}}，而{{gold_standard_system}}设置则需要{{traditional_requirements}}。图{{figure_number}}中在{{measurement_period}}期间测量的{{signal_types}}比较了在检测{{analysis_targets}}时的数据质量。
```

**写作要点**:
- 展示临床对比研究的设置
- 突出设备数量和复杂性的对比
- 强调信号质量的可比性
- 体现临床应用的可行性

**示例**:
```
本研究通过将制作的可穿戴睡眠设备与黄金标准PSG设置进行横向比较，展示了其性能。图4A中的照片展示了睡眠诊所中的一位参与者戴着两种不同系统；额头和下巴上有两个不显眼的可穿戴贴片，而PSG设置则需要15个以上有线笨重传感器和独立数据采集系统。图4B中在睡眠期间测量的生理信号比较了在检测五个睡眠阶段（清醒、N1、N2、N3和快速眼动（REM））时的数据质量。
```

### 4.2 自动化分析与临床准确性验证
[[LLM: Present automated analysis results and clinical accuracy validation with statistical comparisons.]]

**模板结构**:
```
我们系统的平均{{performance_metric_1}}值（{{our_system_value}}）与{{gold_standard_system}}（{{gold_standard_value}}）相当。{{performance_comparison}}来自{{device_description}}在{{reference_position}}的信号，这是一种可接受的替代位置。信号质量通过对两种系统的{{analysis_type}}进行评分进行了进一步验证。{{evaluation_personnel}}以盲数据集进行评分，以避免偏见。总结如图{{figure_number}}所示，代表性的{{analysis_method}}分析结果展示了{{comparison_examples}}，显示了两个系统之间高度一致（{{accuracy_1}}和{{accuracy_2}}），具有很高的{{statistical_measure}}值（{{statistical_value_1}}和{{statistical_value_2}}）。
```

**写作要点**:
- 提供具体的性能指标对比
- 强调盲法评估的客观性
- 报告统计一致性指标
- 突出临床级别的准确性
- **新增**：强调Spearman相关分析和Bland-Altman分析
- **新增**：报告组内相关系数(ICC)等可靠性指标
- **新增**：明确统计显著性水平(p值)

**示例**:
```
我们系统与参考微导管的Spearman相关系数达到0.90578（p<0.001），表明两系统具有高度正相关性。Bland-Altman分析显示平均偏差为-0.1738（95% CI: -1.1268至0.7791），多数数据点落在95%置信区间内。信号质量通过对两种系统的颅内压数据进行对比验证。研究人员以盲法分析数据集，以避免偏见。总结如图5(D和E)所示，统计分析结果展示了高度一致性，组内相关系数0.7958进一步佐证了该方法的可靠性，符合ANSI和AAMI制定的医疗器械标准要求。
```

**新增样本论文风格示例**:
```
我们系统与NICU监护仪的Bland-Altman心率分析表明，在$\mathrm{n} = {66},{656}$个数据点中，本设备与医院监护仪具有$\mathrm{R}2 = {0.99}$的一致性($\pm  {1.6}\mathrm{{BPM}}$)，偏差为${0.02}\mathrm{{BPM}}$。前额血氧仪与医院监护仪数据高度吻合，本设备稳定性凸显其临床价值。信号质量通过Pan-Tompkins算法对QRS波群的检测进行验证，实现心率和呼吸频率的实时测定。总结如图4b-c所示，本设备与NICU监护仪的心率及呼吸频率测量结果高度吻合，展现了临床级监测精度。
```

**新增技术导向示例**:
```
我们系统的平均信噪比值（22.77 dB）与PSG（25.52 dB）相当。较低的信号幅度来自可贴片在鼻子参考位置的信号，这是一种可接受的替代位置。信号质量通过对两种系统的睡眠数据进行评分进行了进一步验证。睡眠技师以盲数据集进行评分，以避免偏见。总结如图4(E和F)所示，代表性的手动评分分析结果展示了两个例子，显示了两个系统之间高度一致（87.50%和88.19%），具有很高的Cohen's kappa（κ）值（0.80和0.82）。
```

## 5. 商业应用与系统集成

### 5.1 实际应用系统架构
[[LLM: Present real-world application architecture with system integration and commercial viability analysis.]]

**模板结构**:
```
**图{{figure_number}}A**显示了使用我们的{{system_name}}的整体{{application_type}}系统。数据传输结构简单：{{system_components}}。在本研究中，{{component_number}}个设备按顺序工作以进行{{system_function}}：{{component_descriptions}}。最后，{{control_device}}作为主要设备来计算{{processing_function}}，如**图{{figure_number}}C**所示。
```

**写作要点**:
- 展示完整的应用系统架构
- 强调系统集成的简洁性
- 描述各组件的协作关系
- 突出主控设备的核心作用

**示例**:
```
**图5A**显示了使用我们的CCB的整体安全系统。数据传输结构简单：用户胸骨上的可穿戴设备、移动设备和无线门锁系统。在本研究中，三个设备按顺序工作以进行安全的生物识别认证：一个带有线性执行器的RF接收器锁，用于解锁锁，一个集成了BLE开发套件的RF遥控器。最后，移动设备作为主要设备来计算认证系统，如**图5C**所示。
```

### 5.2 商业应用演示与性能验证
[[LLM: Demonstrate commercial applications with performance validation and user experience analysis.]]

**模板结构**:
```
视频{{video_number}}中演示了这种{{application_type}}应用。系统通过{{software_platform}}中的{{security_api}}应用安全功能，用于{{security_functions}}，从而允许数据的安全处理。{{algorithm_type}}模型嵌入在带有{{framework_name}}的{{platform_name}}应用程序中。{{algorithm_type}}模型可以实时提取{{data_types}}，以进行{{processing_functions}}。应用{{algorithm_type}}模型后，将注册新用户的处理数据以进行{{authentication_process}}。
```

**写作要点**:
- 提供实际应用的视频演示证据
- 详细描述安全实现机制
- 强调实时处理能力
- 展示用户注册和认证流程

**示例**:
```
视频S1中演示了这种安全应用。系统通过Jetpack Android Studio库中的Android Keystore API应用安全功能，用于加密和解密（或存储和访问）生物识别数据输入/输出流，从而允许数据的安全处理。CNN模型嵌入在带有TensorFlow的Android应用程序中。CNN模型可以实时提取S1和S2数据，以进行用户特征检测和认证。应用CNN模型后，将注册新用户的处理数据以进行认证。
```

### 5.3 用户体验与市场可行性
[[LLM: Analyze user experience and market feasibility with practical demonstration results.]]

**模板结构**:
```
此外，由于第二个用户不是{{algorithm_type}}算法的{{training_data_size}}个训练数据之一，{{system_name}}系统识别为未注册个人尝试{{access_attempt}}。如果我们扩大样本量，准确性将提高并保持接近{{target_accuracy}}的准确率。所展示的工作可以找到使用{{technology_type}}系统的其他应用，包括{{application_list}}。这些应用展示了系统从{{development_stage}}到{{commercial_stage}}的成功转化，为{{market_potential}}奠定了基础。
```

**写作要点**:
- 展示系统的安全性验证
- 分析扩展性和准确性提升潜力
- 列举多种商业应用场景
- 强调技术转化和市场潜力

**示例**:
```
此外，由于第二个用户不是ML算法的20个训练数据之一，CCB系统识别为未注册个人尝试解锁门。如果我们扩大样本量，准确性将提高并保持接近100%的准确率。所展示的工作可以找到使用可穿戴心脏生物识别系统的其他应用，包括机密文件传输、银行安全、双因素认证和安全的远程患者监测。这些应用展示了系统从实验室研究到商业产品的成功转化，为广阔的市场应用奠定了基础。
```

## 6. 智能算法性能与疾病检测

### 5.1 机器学习自动分析结果
[[LLM: Present machine learning algorithm performance with clinical validation results.]]

**模板结构**:
```
图{{figure_number}}中的图表展示了{{algorithm_application}}的两个代表性结果。当{{device_description}}的{{analysis_method}}数据与{{gold_standard_analysis}}进行比较时，两种方法之间有很强的一致性，准确率分别为{{accuracy_1}}和{{accuracy_2}}，{{statistical_measure}}值分别为{{statistical_value_1}}和{{statistical_value_2}}。{{analysis_comparison}}的相关结果与图{{reference_figure}}中的{{reference_analysis}}数据类似。图{{figure_number}}中的混淆矩阵总结了我们的{{algorithm_type}}与{{gold_standard_method}}相比的表现。{{analysis_method}}与{{reference_method}}的整体一致性和{{statistical_measure}}（{{overall_accuracy}}和{{overall_statistical_value}}）显示出比报道的平均{{comparison_metric}}（{{reported_average}}）更好的表现。
```

**写作要点**:
- 展示算法的临床验证结果
- 提供具体的准确性数据
- 使用混淆矩阵等标准评价方法
- 与文献报告的性能进行对比

**示例**:
```
图6（C和D）中的图表展示了自动睡眠评分的两个代表性结果。当可穿戴贴片的自动评分数据与PSG评分进行比较时，两种方法之间有很强的一致性，准确率分别为88.41%和88.17%，Cohen's kappa值分别为0.81和0.82。睡眠评分的相关结果与图4中的手动评分数据类似。图6E中的混淆矩阵总结了我们的CNN算法与PSG手动评分相比的表现。自动评分与手动评分的整体一致性和Cohen's kappa（83.89%和0.76）显示出比报道的平均评分一致性（82.0%）更好的表现。
```

### 5.2 疾病检测与诊断准确性
[[LLM: Present disease detection results with sensitivity, specificity, and clinical validation data.]]

**模板结构**:
```
图{{figure_number}}中的{{analysis_results}}展示了{{disease_detection_capability}}。{{detection_algorithm}}能够准确识别{{target_condition}}，与{{gold_standard_diagnosis}}相比，{{sensitivity_metric}}为{{sensitivity_value}}，{{specificity_metric}}为{{specificity_value}}。{{receiver_operating_characteristic}}分析显示{{auc_value}}的{{auc_metric}}，表明{{diagnostic_performance}}。在{{patient_population}}的临床验证中，{{detection_system}}成功检测出{{detection_rate}}的{{target_condition}}病例，{{false_positive_rate}}的假阳性率。
```

**写作要点**:
- 提供疾病检测的敏感性和特异性
- 使用ROC分析等标准诊断评价方法
- 报告临床验证的检测率
- 强调与金标准诊断的一致性

**示例**:
```
图7中的呼吸暂停检测结果展示了自动呼吸暂停事件识别能力。CNN算法能够准确识别阻塞性睡眠呼吸暂停，与PSG诊断相比，敏感性为88.5%，特异性为85.2%。受试者工作特征（ROC）分析显示0.91的曲线下面积（AUC），表明优秀的诊断性能。在睡眠障碍患者的临床验证中，可穿戴系统成功检测出92.3%的呼吸暂停病例，7.8%的假阳性率。
```

---

## 📋 子模板选择决策树

```
开始 → 确定研究类型
├─ 临床医学研究
│  ├─ 多场景验证 → 4.2 + 4.3
│  ├─ 单一应用 → 4.2
│  └─ 详细数据 → 4.3
├─ 工程技术创新
│  ├─ 算法重点 → 4.1 + 4.6
│  ├─ 材料重点 → 4.4 + 4.6
│  └─ 综合创新 → 4.1 + 4.4 + 4.6
├─ 产品开发研究
│  ├─ 现代格式 → 4.5 + 4.6
│  ├─ 传统格式 → 4.1 + 4.6
│  └─ 市场导向 → 4.6
└─ 基础技术研究
   ├─ 算法创新 → 4.1 + 4.3
   ├─ 方法学 → 4.3
   └─ 对比研究 → 4.1 + 4.6
```

## 🔗 子模板交叉引用指南

### **内容互补关系**
- **4.1 ↔ 4.4**: 算法软件 ↔ 硬件性能 (完整技术验证)
- **4.2 ↔ 4.3**: 临床应用 ↔ 详细数据 (医学期刊标准)
- **4.5 ↔ 4.6**: 综合展示 ↔ 竞争分析 (现代产品研究)

### **期刊适配建议**
- **Nature Medicine**: 4.2 + 4.3 (临床 + 数据)
- **Nature/Science**: 4.1 + 4.4 + 4.6 (技术 + 性能 + 对比)
- **Applied Sciences**: 4.5 + 4.6 (应用 + 市场)
- **IEEE Transactions**: 4.1 + 4.3 (算法 + 技术细节)

## 📊 质量检查清单

### 模板选择合理性
- [ ] 研究类型与模板选择匹配
- [ ] 期刊要求与模板格式对应
- [ ] 内容重点与模板功能一致
- [ ] 模板组合逻辑清晰

### 内容完整性
- [ ] 核心结果全面展示
- [ ] 关键数据详实准确
- [ ] 验证方法科学合理
- [ ] 创新点突出明确

### 逻辑连贯性
- [ ] 子模板间逻辑一致
- [ ] 内容层次清晰递进
- [ ] 论证链条完整
- [ ] 结论支撑充分

### 应用价值体现
- [ ] 实际应用场景明确
- [ ] 用户价值体现充分
- [ ] 技术优势突出
- [ ] 市场前景清晰

---

## 📝 使用建议总结

1. **首次使用**: 建议从单一子模板开始，熟悉结构和要求
2. **组合使用**: 根据研究复杂度逐步增加子模板数量
3. **灵活调整**: 根据期刊要求和审稿意见灵活调整模板组合
4. **质量控制**: 使用质量检查清单确保内容完整性和逻辑性

通过合理选择和组合使用这些子模板，可以构建出适合不同研究类型和期刊要求的高质量结果部分。
